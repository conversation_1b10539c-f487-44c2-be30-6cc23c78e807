# Base on the official Playwright image (matches docs)
FROM mcr.microsoft.com/playwright:v1.55.0-noble

# Use non-root user as recommended for scraping/untrusted sites
USER root

# Install X server (Xvfb), VNC server, noVNC, websockify, and lightweight WM
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        xvfb \
        x11vnc \
        x11-utils \
        novnc \
        websockify \
        openbox \
        fluxbox \
    && rm -rf /var/lib/apt/lists/*

# Prepare noVNC web root symlink (common path)
RUN if [ ! -d /usr/share/novnc ]; then mkdir -p /usr/share/novnc; fi

# Workdir and copy startup script
WORKDIR /home/<USER>
COPY scripts/start.sh /usr/local/bin/start.sh
RUN chmod +x /usr/local/bin/start.sh \
    && chown -R pwuser:pwuser /home/<USER>

# Expose default ports (can be overridden per-container)
EXPOSE 3000 5900 6080

# Run as root (Chromium sandbox disabled by default in root mode as per Playwright docs)
USER root

# Entrypoint launches Xvfb + VNC + noVNC + Playwright server
ENTRYPOINT ["/usr/local/bin/start.sh"]
