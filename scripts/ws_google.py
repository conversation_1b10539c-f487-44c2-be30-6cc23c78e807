from playwright.sync_api import sync_playwright
import os
import time
from datetime import datetime

# Config
WS_ENDPOINT = os.environ.get("PW_WS", "ws://127.0.0.1:3001/")
URL = os.environ.get("TARGET_URL", "https://www.google.com")
STAY_MS = int(os.environ.get("STAY_MS", "10000"))
SCREENSHOT_PATH = os.environ.get("SCREENSHOT_PATH", "screenshots")


def main() -> None:
    # Create screenshots directory if it doesn't exist
    os.makedirs(SCREENSHOT_PATH, exist_ok=True)

    print(f"[client] Connecting to {WS_ENDPOINT}")
    with sync_playwright() as p:
        # Connect to remote Playwright server (Chromium)
        browser = p.chromium.connect(ws_endpoint=WS_ENDPOINT)
        try:
            context = browser.new_context()
            page = context.new_page()
            print(f"[client] Goto {URL}")
            page.goto(URL, wait_until="domcontentloaded")

            # Wait a bit for the page to fully load
            page.wait_for_timeout(2000)

            # Take screenshot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_file = os.path.join(SCREENSHOT_PATH, f"google_{timestamp}.png")
            page.screenshot(path=screenshot_file, full_page=True)
            print(f"[client] Screenshot saved to: {screenshot_file}")

            print(f"[client] Stay for {STAY_MS} ms")
            page.wait_for_timeout(STAY_MS)
        finally:
            print("[client] Closing browser")
            browser.close()


if __name__ == "__main__":
    main()
